#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from http.server import HTTPServer, BaseHTTPRequestHandler

class MYJServerHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/get_base':
            self.send_base_file()
        else:
            self.send_error(404, "Path not found")

    def send_base_file(self):
        try:
            base_file_path = os.path.join(os.path.dirname(__file__), 'base.py')

            if not os.path.exists(base_file_path):
                self.send_error(404, "base.py file not found")
                return

            with open(base_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            self.send_response(200)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(file_content.encode('utf-8'))

        except Exception as e:
            self.send_error(500, f"Error reading base.py: {str(e)}")

def main():
    server = HTTPServer(('localhost', 8080), MYJServerHandler)
    print("服务器启动在 http://localhost:8080")
    print("访问 /get_base 获取base.py内容")
    server.serve_forever()

if __name__ == '__main__':
    main()