#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler

class MYJServerHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/get_files':
            self.send_files()
        else:
            self.send_error(404, "Path not found")

    def send_files(self):
        try:
            files = {}

            # 读取base.py
            base_file_path = os.path.join(os.path.dirname(__file__), 'base.py')
            if os.path.exists(base_file_path):
                with open(base_file_path, 'r', encoding='utf-8') as f:
                    files['base.py'] = f.read()

            # 读取js_parser.py
            parser_file_path = os.path.join(os.path.dirname(__file__), 'js_parser.py')
            if os.path.exists(parser_file_path):
                with open(parser_file_path, 'r', encoding='utf-8') as f:
                    files['js_parser.py'] = f.read()

            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.end_headers()
            self.wfile.write(json.dumps(files, ensure_ascii=False).encode('utf-8'))

        except Exception as e:
            self.send_error(500, f"Error reading files: {str(e)}")

def main():
    server = HTTPServer(('localhost', 8080), MYJServerHandler)
    print("服务器启动在 http://localhost:8080")
    print("访问 /get_files 获取多个文件内容")
    server.serve_forever()

if __name__ == '__main__':
    main()