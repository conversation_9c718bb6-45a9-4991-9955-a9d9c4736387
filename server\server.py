#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MYJ在线项目 - 服务器端
功能：接收客户端请求，返回base.py文件内容
"""

import os
from http.server import HTTPServer, BaseHTTPRequestHandler
import json
from urllib.parse import urlparse

class MYJServerHandler(BaseHTTPRequestHandler):
    """HTTP请求处理器"""

    def do_GET(self):
        """处理GET请求"""
        try:
            # 解析URL路径
            parsed_path = urlparse(self.path)

            if parsed_path.path == '/get_base':
                # 返回base.py文件内容
                self.send_base_file()
            elif parsed_path.path == '/status':
                # 返回服务器状态
                self.send_status()
            else:
                # 404错误
                self.send_error(404, "Path not found")

        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")

    def do_POST(self):
        """处理POST请求"""
        try:
            if self.path == '/get_base':
                self.send_base_file()
            else:
                self.send_error(404, "Path not found")
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")

    def send_base_file(self):
        """发送base.py文件内容"""
        try:
            # 获取base.py文件路径
            base_file_path = os.path.join(os.path.dirname(__file__), 'base.py')

            if not os.path.exists(base_file_path):
                self.send_error(404, "base.py file not found")
                return

            # 读取文件内容
            with open(base_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # 构建响应数据
            response_data = {
                'status': 'success',
                'message': 'base.py content retrieved successfully',
                'file_path': base_file_path,
                'file_size': len(file_content),
                'content': file_content
            }

            # 发送响应
            self.send_response(200)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')  # 允许跨域
            self.end_headers()

            # 发送JSON数据
            json_data = json.dumps(response_data, ensure_ascii=False, indent=2)
            self.wfile.write(json_data.encode('utf-8'))

            print(f"[服务器] 已向客户端 {self.client_address[0]} 发送base.py文件内容")

        except Exception as e:
            self.send_error(500, f"Error reading base.py: {str(e)}")

    def send_status(self):
        """发送服务器状态"""
        status_data = {
            'status': 'running',
            'message': 'MYJ Server is running',
            'server_info': {
                'version': '1.0.0',
                'description': 'MYJ在线项目服务器'
            }
        }

        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()

        json_data = json.dumps(status_data, ensure_ascii=False, indent=2)
        self.wfile.write(json_data.encode('utf-8'))

        print(f"[服务器] 已向客户端 {self.client_address[0]} 发送状态信息")

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{self.log_date_time_string()}] {format % args}")

class MYJServer:
    """MYJ服务器类"""

    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server = None
        self.running = False

    def start(self):
        """启动服务器"""
        try:
            self.server = HTTPServer((self.host, self.port), MYJServerHandler)
            self.running = True

            print(f"[服务器] MYJ服务器启动成功!")
            print(f"[服务器] 监听地址: http://{self.host}:{self.port}")
            print(f"[服务器] 可用接口:")
            print(f"[服务器]   GET  /get_base - 获取base.py文件内容")
            print(f"[服务器]   POST /get_base - 获取base.py文件内容")
            print(f"[服务器]   GET  /status   - 获取服务器状态")
            print(f"[服务器] 按 Ctrl+C 停止服务器")
            print("-" * 50)

            # 启动服务器
            self.server.serve_forever()

        except KeyboardInterrupt:
            print(f"\n[服务器] 收到停止信号，正在关闭服务器...")
            self.stop()
        except Exception as e:
            print(f"[服务器] 启动失败: {str(e)}")
            self.running = False

    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.running = False
            print(f"[服务器] 服务器已停止")

def main():
    """主函数"""
    # 创建并启动服务器
    server = MYJServer(host='localhost', port=8080)
    server.start()

if __name__ == '__main__':
    main()