#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MYJ在线项目 - 客户端
功能：请求服务器并获取base.py文件内容
"""

import requests
import json
import sys
from datetime import datetime

class MYJClient:
    """MYJ客户端类"""
    
    def __init__(self, server_host='localhost', server_port=8080):
        self.server_host = server_host
        self.server_port = server_port
        self.base_url = f"http://{server_host}:{server_port}"
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'MYJ-Client/1.0.0',
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def get_base_file(self, method='GET'):
        """获取base.py文件内容"""
        try:
            print(f"[客户端] 正在请求服务器获取base.py文件内容...")
            print(f"[客户端] 服务器地址: {self.base_url}")
            print(f"[客户端] 请求方法: {method}")
            print(f"[客户端] 请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print("-" * 50)
            
            # 发送请求
            if method.upper() == 'GET':
                response = self.session.get(f"{self.base_url}/get_base", timeout=30)
            else:
                response = self.session.post(f"{self.base_url}/get_base", timeout=30)
            
            # 检查响应状态
            if response.status_code == 200:
                # 解析JSON响应
                data = response.json()
                
                print(f"[客户端] ✅ 请求成功!")
                print(f"[客户端] 响应状态: {data.get('status', 'unknown')}")
                print(f"[客户端] 响应消息: {data.get('message', 'no message')}")
                print(f"[客户端] 文件路径: {data.get('file_path', 'unknown')}")
                print(f"[客户端] 文件大小: {data.get('file_size', 0)} 字符")
                print("-" * 50)
                
                # 获取文件内容
                file_content = data.get('content', '')
                
                if file_content:
                    print(f"[客户端] 📄 base.py文件内容:")
                    print("=" * 80)
                    print(file_content)
                    print("=" * 80)
                    
                    # 保存到本地文件
                    self.save_content_to_file(file_content)
                else:
                    print(f"[客户端] ⚠️ 警告: 文件内容为空")
                
                return True
                
            else:
                print(f"[客户端] ❌ 请求失败!")
                print(f"[客户端] HTTP状态码: {response.status_code}")
                print(f"[客户端] 错误信息: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print(f"[客户端] ❌ 连接错误: 无法连接到服务器 {self.base_url}")
            print(f"[客户端] 请确保服务器正在运行")
            return False
        except requests.exceptions.Timeout:
            print(f"[客户端] ❌ 请求超时: 服务器响应时间过长")
            return False
        except json.JSONDecodeError as e:
            print(f"[客户端] ❌ JSON解析错误: {str(e)}")
            print(f"[客户端] 服务器响应: {response.text[:200]}...")
            return False
        except Exception as e:
            print(f"[客户端] ❌ 未知错误: {str(e)}")
            return False
    
    def get_server_status(self):
        """获取服务器状态"""
        try:
            print(f"[客户端] 正在检查服务器状态...")
            
            response = self.session.get(f"{self.base_url}/status", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"[客户端] ✅ 服务器状态正常")
                print(f"[客户端] 状态: {data.get('status', 'unknown')}")
                print(f"[客户端] 消息: {data.get('message', 'no message')}")
                
                server_info = data.get('server_info', {})
                if server_info:
                    print(f"[客户端] 服务器版本: {server_info.get('version', 'unknown')}")
                    print(f"[客户端] 服务器描述: {server_info.get('description', 'no description')}")
                
                return True
            else:
                print(f"[客户端] ❌ 服务器状态异常: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"[客户端] ❌ 检查服务器状态失败: {str(e)}")
            return False
    
    def interactive_menu(self):
        """交互式菜单"""
        while True:
            print("\n" + "=" * 60)
            print("🎮 MYJ客户端 - 交互式菜单")
            print("=" * 60)
            print("1. 获取base.py文件内容 (GET请求)")
            print("2. 获取base.py文件内容 (POST请求)")
            print("3. 检查服务器状态")
            print("4. 修改服务器地址")
            print("5. 退出程序")
            print("-" * 60)
            
            try:
                choice = input("请选择操作 (1-5): ").strip()
                
                if choice == '1':
                    self.get_base_file('GET')
                elif choice == '2':
                    self.get_base_file('POST')
                elif choice == '3':
                    self.get_server_status()
                elif choice == '4':
                    self.change_server_address()
                elif choice == '5':
                    print("[客户端] 👋 再见!")
                    break
                else:
                    print("[客户端] ⚠️ 无效选择，请输入1-5之间的数字")
                
                # 等待用户按键继续
                if choice in ['1', '2', '3']:
                    input("\n按回车键继续...")
                    
            except KeyboardInterrupt:
                print("\n[客户端] 👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"[客户端] ❌ 操作错误: {str(e)}")
    
    def change_server_address(self):
        """修改服务器地址"""
        try:
            print(f"[客户端] 当前服务器地址: {self.base_url}")
            new_host = input("请输入新的服务器主机地址 (默认: localhost): ").strip()
            new_port = input("请输入新的服务器端口 (默认: 8080): ").strip()
            
            if not new_host:
                new_host = 'localhost'
            if not new_port:
                new_port = '8080'
            
            # 验证端口号
            try:
                port_num = int(new_port)
                if not (1 <= port_num <= 65535):
                    raise ValueError("端口号必须在1-65535之间")
            except ValueError as e:
                print(f"[客户端] ❌ 端口号无效: {str(e)}")
                return
            
            # 更新服务器地址
            self.server_host = new_host
            self.server_port = int(new_port)
            self.base_url = f"http://{new_host}:{new_port}"
            
            print(f"[客户端] ✅ 服务器地址已更新为: {self.base_url}")
            
        except Exception as e:
            print(f"[客户端] ❌ 修改服务器地址失败: {str(e)}")

def main():
    """主函数"""
    print("🚀 MYJ客户端启动中...")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--help' or sys.argv[1] == '-h':
            print("使用方法:")
            print("  python client.py                    # 启动交互式菜单")
            print("  python client.py --auto             # 自动获取文件内容")
            print("  python client.py --status           # 检查服务器状态")
            print("  python client.py --help             # 显示帮助信息")
            return
    
    # 创建客户端实例
    client = MYJClient()
    
    # 根据命令行参数执行不同操作
    if len(sys.argv) > 1:
        if sys.argv[1] == '--auto':
            print("[客户端] 🤖 自动模式: 获取base.py文件内容")
            client.get_base_file()
        elif sys.argv[1] == '--status':
            print("[客户端] 📊 检查服务器状态")
            client.get_server_status()
        else:
            print(f"[客户端] ⚠️ 未知参数: {sys.argv[1]}")
            print("[客户端] 使用 --help 查看帮助信息")
    else:
        # 启动交互式菜单
        client.interactive_menu()

if __name__ == '__main__':
    main()
