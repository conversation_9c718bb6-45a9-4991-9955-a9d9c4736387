#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def get_files():
    try:
        response = requests.get('http://localhost:8080/get_files')
        if response.status_code == 200:
            files = response.json()
            for filename, content in files.items():
                print(f"执行 {filename}:")
                print("-" * 50)
                exec(content)
                print("-" * 50)
        else:
            print(f"请求失败: {response.status_code}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    get_files()
