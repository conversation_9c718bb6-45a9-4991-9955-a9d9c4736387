#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

def get_base_file():
    try:
        response = requests.get('http://localhost:8080/get_base')
        if response.status_code == 200:
            print("获取到的base.py内容:")
            print("-" * 50)
            code = response.text
            exec(code)
        else:
            print(f"请求失败: {response.status_code}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    get_base_file()
