import requests
import asyncio
import time
from urllib.parse import quote  # 登录时,把中文账号转码使用
from yzm import 获取验证码            # 获取验证码

class MYJBase:
    def __init__(self,config_filename):
        self.版本 = '最低在线版'
        self.版本号 = '1.0.0'
        self.登录配置 = {
            '区服': str,
            '账号': str,
            '密码': str,
            '角色序号': str,
        }
        self.全局配置 = {
            '延时': float,
            '非操作延时': float,
            '邮箱': str,
            '百分比经验上限': int,
            '日志输出': bool,
            '在线控制器': bool,
        }
        self.日常开关 = {
            '拖把每日': bool,
            '在线礼包': bool,
            '竞技场': bool,
            '魔化': bool,
            '如意': bool,
            '乐园': bool,
            '沼泽': bool,
            '诅咒': bool,
        }
        self.活动开关 = {
            '无双': bool,
            '逆无双': bool,
            '通天': bool,
            '罗汉': bool,
        }
        self.角色信息 = {
            '角色名': str,
            '角色ID': str,
        }
        self.网络信息 = {
            'cookies': None
        }

    def 控制台日志(self, *args):
        if self.全局配置['日志输出']:
            text = ' '.join(str(arg) for arg in args)
            print(f'{self.登录配置['区服']}=>{self.角色信息['角色名']}==>{text}')

    def 保持在线(self):

        def _确认服务器是否维护():
            while True:
                res = requests.get(f'http://{self.登录配置['区服']}.pet.imop.com')
                if res.status_code == 200:
                    self.控制台日志(f'服务器{self.登录配置['区服']}不在维护,可登录')
                    return True
                else:
                    # self.print(res.status_code)
                    (f'服务器{self.登录配置['区服']}状态非正常,返回状态码{res.status_code},等待继续检测')
                    time.sleep(2)

        def _登录():
            _error_code = {
                '0': "登录失败,请重新登录",
                '1': "签名验证失败",
                '2': "时间戳过期",
                '3': "参数为空或格式不正确",
                '4': "用户名密码验证未通过",
                '5': "用户已被锁定",
                '6': "密保未通过",
                '7': "cookie验证未通过",
                '8': "token验证未通过",
                '9': "大区验证未通过",
                '11': "验证码错误",
                '12': "验证码为空",
                '999': "系统异常，登录失败"
            }
            while True:
            # 登录发送信息
                login_post = {
                    'url': f'http://{self.登录配置['区服']}.pet.imop.com/LoginAction.jsp',
                    'cookies': {'mopet_logon': '123'},
                    'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f'http://{self.登录配置['区服']}.pet.imop.com',
                        'Referer': f'http://{self.登录配置['区服']}.pet.imop.com/login.html',
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                    },
                    'data': {
                        'user_name': quote(self.登录配置['账号'], encoding="gbk"),
                        'password': self.登录配置['密码'],
                        'checkcode': 获取验证码()
                    },
                }
                # 登录返回信息
                login_response = requests.post(**login_post)
                if r'document.location="/pet.jsp"' in login_response.text:
                    # print(login_response.text)
                    self.网络信息['cookies'] = login_response.cookies
                    self.控制台日志('登陆成功')
                    return True
                else:
                    for _code in _error_code.keys():
                        if f'errCode={_code}"' in login_response.text:
                            if _code == '11':
                                self.控制台日志('错误码为11,验证码错误,重新获取验证码登录')
                                continue
                            else:
                                self.print(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                                return False

        def _进入角色():
            pass
        
        async def _异步在线():
            while True:
                while self.全局配置['在线控制器'] is False:
                    self.控制台日志('在线控制器已关闭,暂不登录')
                    await asyncio.sleep(3)
                
