import os
import requests
import re
import asyncio
import time
import random
import json
import base64
from urllib.parse import quote  # 登录时,把中文账号转码使用
import httpx
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
import datetime
from PyGameAuto32 import Thread
from threading import Lock      # 线程锁,用在命令

# from js_parser import parse_js_code, ret_packages, js_to_python # 解析js代码

globals()['extra_codes'] = []

try:
    with open(os.getcwd() + '\\js_parser.py', 'r', encoding='utf-8') as f:
        content = f.read()
        exec(content)
        extra_codes.append(content)
except:
    pass
    
全局_验证码接口 = 'http://*************:5100/myj_yzm_yyy'

def 获取验证码(proxies=None):
    """
    return X-TEMP_INT
    """
    # 取随机验证码尾数
    while True:
        temp_int = random.randint(2222, 8888)
        # 获取随机验证码
        # requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed=2345',proxies=None,timeout=5).content
        try:
            yzm_data = requests.get(f'http://randimg.gc.imop.com/com/randimg/img_plus.php?rnd_seed={temp_int}', proxies=proxies).content
            # print(yzm_data)
        # 打码自己的服务器获取登录验证码
            response = requests.post(全局_验证码接口, data=base64.b64encode(yzm_data))
            # print(response.text)
        # 组合计算结果以及原始验证码尾数
            _res = json.loads(response.text).get('res')
            # print(_res)
            return str(_res) + str(temp_int)
        except Exception as e:
            return '0' + str(temp_int)

def 通用_发送邮件(receiver: str, content):
    smtp_server = "smtp.qq.com"
    sender_email = "<EMAIL>"
    # 构建邮件对象
    msg = MIMEMultipart()
    msg['From'] = '<EMAIL>'
    msg["To"] = receiver
    msg["Subject"] = '小叮当-极-提醒邮件'
    body = f'{(datetime.datetime.now()+ datetime.timedelta(days=-1)).strftime("%Y-%m-%d")}\n' + ' '.join(str(arg) for arg in content)
    msg.attach(MIMEText(body, 'plain'))
    server = smtplib.SMTP(smtp_server, 587)
    server.starttls()
    server.login(sender_email, 'eidkyaigsbgdbabg')
    server.sendmail(sender_email, receiver, msg.as_string())
    server.quit()

def 通用_获取字符串时间():
    return datetime.datetime.now().strftime('%H:%M:%S')

class MYJBase:
    def __init__(self,configs):
        self.版本 = '最低在线版'
        self.版本号 = '1.0.0'
        self.登录配置 = {
            '区服': configs.get('区服', 'x12'),
            '账号': configs.get('账号', 'wk93qlyy1'),
            '密码': configs.get('密码', '00000000'),
            '角色序号': configs.get('角色序号', '0'),
        }
        self.全局配置 = {
            '延时': configs.get('延时', 0.5),
            '非操作延时': configs.get('非操作延时', 1.5),
            '邮箱': configs.get('邮箱', '<EMAIL>'),
            '百分比经验上限': configs.get('百分比经验上限', 100),
            '日志输出': configs.get('日志输出', True),
            '在线控制器': configs.get('在线控制器', True),
            '重连时间': configs.get('重连时间', 300),
        }
        self.日常开关 = {
            '拖把每日': configs.get('拖把每日', False),
            '在线礼包': configs.get('在线礼包', False),
            '竞技场': configs.get('竞技场', False),
            '魔化': configs.get('魔化', False),
            '如意': configs.get('如意', False),
            '乐园': configs.get('乐园', False),
            '沼泽': configs.get('沼泽', False),
            '诅咒': configs.get('诅咒', False),
        }
        self.活动开关 = {
            '无双': configs.get('无双', False),
            '逆无双': configs.get('逆无双', False),
            '通天': configs.get('通天', False),
            '罗汉': configs.get('罗汉', False)
        }
        self.角色信息 = {
            '角色名': str,
            '角色ID': str,
            '等级': int,
            '地图': str,
            '房间': str,
            'hp_left': int,
            'hp_max': int,
            'sp_left': int,
            'sp_max': int,
            '经验百分比': int,
            'myUserId': str,
            'validateParam': str,
            '战斗状态': bool
        }
        self.网络信息 = {
            'cookies': None
        }
        self.地址 = {
            '角色地址': str,
            '保持在线地址': str
        }
        self.正则 = {
            'npc': re.compile(r"p\._getNpc\(([^)]*)\)"),
            'modify': re.compile(r'<[A-Za-z0-9 ="\':;/_\-.]*>'),
            'petLv': re.compile(r"petLv=(\d+);"),   # 宠物等级
            'nowMap': re.compile(r'nowMap\s*=\s*"([^"]+)"'),    # 当前所在地图
            'room': re.compile(r'room\s*=\s*"([^"]+)"'),    # 当前所在房间
            'hp_left': re.compile(r"<span id='hpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余hp
            'hp_max': re.compile(r"<span id='hpLine_left_max_no'[^>]*>(\d+)</span>"),   # 最大hp
            'sp_left': re.compile(r"<span id='mpLine_left_no'[^>]*>(\d+)</span>"),  # 剩余sp
            'sp_max': re.compile(r"<span id='mpLine_left_max_no'>(\d+)</span>"),    # 最大sp
            'exp': re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE),   # 百分比经验
            'join_role': re.compile(r"selectimg\(\s*(\d+)\s*,\s*'click'\s*,\s*(\d+)[^)]*\)\s*.*?"
                                    r'<div\s+align="center"\s+style="position:relative;top:-10px">(.*?)</div>', re.DOTALL),  # 获取角色代码及序号部分
            # 'skills': re.compile(r"p\.cutArray\[(?:[0-5])\]='(perform.*?)';"),  #
            'petName': re.compile(r'petName\s?=\s?"(.*)";'),    # 宠物名
            'myUserId': re.compile(r'myUserId\s?=\s?"(.*)";'),  # id
            'validateParam': re.compile(r'validateParam\s?=\s?"(.*)";'),    # 链接验证参数
            'combat_start': re.compile(r"p\._combat\s*\(", re.IGNORECASE),      # 进入战斗
            'combat_end': re.compile(r"p\.lost\(\s*p\.petWin\.fighter_2\s*\)", re.IGNORECASE),  # 退出战斗
        }
        self.时间 = {
            '进战': None,
            '出战': None,
        }
        self.状态 = {
            '自己': list,
            '对手': list
        }
        self.消息 = {
            '右上': [],
            '左下': [],
            '右下': [],
            'NPC对话': [],
        }
        self.背包 = {
            '背包道具': [],
            '是否使用': bool
        }
        self.道具说明 = {
            '内容': dict,
            '是否使用': bool,
        }
        self.技能 = {
            '列表': None,
            '是否使用': bool
        }
        self.任务 = {
            '列表': None,
            '是否使用': False,
        }
        self.NPC = {
            '原始': [],
            '可攻击': []
        }
        self.客户端 = httpx.AsyncClient(timeout=None)
        self.命令线程锁 = Lock()
        self.在线线程 = None

    def 控制台日志(self, *args):
        if self.全局配置['日志输出']:
            text = ' '.join(str(arg) for arg in args)
            print(f"{self.登录配置['区服']}=>{self.角色信息['角色名']}==>{text}")

    def 刷新角色信息(self):
        while True:
            response = requests.get(url=f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petid={self.角色信息['角色ID']}", cookies=self.网络信息['cookies'])
            t = response.text
            try:
                self.角色信息['等级'] = self.正则['petLv'].search(t).group(1)
                self.角色信息['地图'] = self.正则['nowMap'].search(t).group(1)
                if self.角色信息['地图'] is None:
                    continue
                self.角色信息['房间'] = self.正则['room'].search(t).group(1)
                if self.角色信息['地图'] is None:
                    continue
                self.角色信息['角色名'] = self.正则['petName'].search(t).group(1)
                self.角色信息['myUserId'] = self.正则['myUserId'].search(t).group(1)
                self.角色信息['validateParam'] = self.正则['validateParam'].search(t).group(1)
                self.角色信息['hp_left'] = self.正则['hp_left'].search(t).group(1)
                self.角色信息['sp_left'] = self.正则['sp_left'].search(t).group(1)
                self.角色信息['hp_max'] = self.正则['hp_max'].search(t).group(1)
                self.角色信息['sp_max'] = self.正则['sp_max'].search(t).group(1)
                self.角色信息['经验百分比'] = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(t).group(1))
                if self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                    self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                    通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}角色{self.角色信息['角色名']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                    #TODO 加入命令锁,锁死命令
                self.地址['保持在线地址'] = f"http://{self.登录配置['区服']}.pet.imop.com:8080/io/{self.角色信息['myUserId']}&{self.角色信息['validateParam']}"
                self.控制台日志('刷新角色属性完成')
                return
            except:
                pass

    def 保持在线(self):

        def _确认服务器是否维护完成():
            while True:
                res = requests.get(f"http://{self.登录配置['区服']}.pet.imop.com")
                if res.status_code == 200:
                    self.控制台日志(f"服务器{self.登录配置['区服']}不在维护,可登录")
                    return True
                else:
                    # self.控制台日志(res.status_code)
                    self.控制台日志(f"服务器{self.登录配置['区服']}状态非正常,返回状态码{res.status_code},等待继续检测")
                    time.sleep(2)

        def _登录():
            _error_code = {
                '0': "登录失败,请重新登录",
                '1': "签名验证失败",
                '2': "时间戳过期",
                '3': "参数为空或格式不正确",
                '4': "用户名密码验证未通过",
                '5': "用户已被锁定",
                '6': "密保未通过",
                '7': "cookie验证未通过",
                '8': "token验证未通过",
                '9': "大区验证未通过",
                '11': "验证码错误",
                '12': "验证码为空",
                '999': "系统异常，登录失败"
            }
            while True:
            # 登录发送信息
                login_post = {
                    'url': f"http://{self.登录配置['区服']}.pet.imop.com/LoginAction.jsp",
                    'cookies': {'mopet_logon': '123'},
                    'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f"http://{self.登录配置['区服']}.pet.imop.com",
                        'Referer': f"http://{self.登录配置['区服']}.pet.imop.com/login.html",
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                    },
                    'data': {
                        'user_name': quote(self.登录配置['账号'], encoding="gbk"),
                        'password': self.登录配置['密码'],
                        'checkcode': 获取验证码()
                    },
                }
                # 登录返回信息
                login_response = requests.post(**login_post)
                if r'document.location="/pet.jsp"' in login_response.text:
                    # print(login_response.text)
                    self.网络信息['cookies'] = login_response.cookies
                    self.控制台日志('登陆成功')
                    return True
                else:
                    for _code in _error_code.keys():
                        if f'errCode={_code}"' in login_response.text:
                            if _code == '11':
                                self.控制台日志('错误码为11,验证码错误,重新获取验证码登录')
                                continue
                            else:
                                self.控制台日志(f'出现非验证码错误,错误类型为=>{_error_code[_code]}')
                                return False

        def _进入角色():
            局_请求信息 = {
                'cookies': self.网络信息['cookies'],
                'headers': {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'Origin': f"http://{self.登录配置['区服']}.pet.imop.com",
                        'Referer': f"http://{self.登录配置['区服']}.pet.imop.com/login.html",
                        'Connection': 'keep-alive',
                        'User-Agent': 'Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 10.0; WOW64; Trident/7.0; .NET4.0C; .NET4.0E)',
                },
            }
            while True:
                局_请求信息['url'] = f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp"
                局_登录请求返回对象 = requests.get(**局_请求信息)
                if 'selectimg' in 局_登录请求返回对象.text:
                    self.控制台日志('当前没有在游戏内的角色')
                    局_角色列表 = self.正则['join_role'].findall(局_登录请求返回对象.text)
                    if 局_角色列表:
                        # 打印所有角色信息
                        for 局_角色信息 in 局_角色列表:
                            self.控制台日志(f"""角色序号[{局_角色信息[0]}]角色名[{局_角色信息[2]}]角色id[{局_角色信息[1]}]""")
                        for 局_角色信息 in 局_角色列表:
                            if int(局_角色信息[0]) == int(self.登录配置['角色序号']):
                                self.角色信息['角色名'] = 局_角色信息[2]
                                self.角色信息['角色ID'] = 局_角色信息[1]
                                self.地址['角色地址'] = f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petId={self.角色信息['角色ID']}"
                                self.控制台日志(f'角色获取成功,目标角色名为{self.角色信息["角色名"]}ID为{self.角色信息["角色ID"]}')
                                while True:
                                    局_进入角色请求信息 = {
                                        'url': f"http://{self.登录配置['区服']}.pet.imop.com/pet.jsp?petid={self.角色信息['角色ID']}",
                                        'cookies': self.网络信息['cookies'],
                                    }
                                    局_进入角色请求返回对象 = requests.get(**局_进入角色请求信息)
                                    if 'showWorldMap' in 局_进入角色请求返回对象.text:
                                        break
                                self.控制台日志('进入角色成功')
                                self.刷新角色信息()
                                return True
                elif 'validateParam' in 局_登录请求返回对象.text:
                    self.控制台日志('当前已经进入角色')
                    self.角色信息['等级'] = self.正则['petLv'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['地图'] = self.正则['nowMap'].search(局_登录请求返回对象.text).group(1)
                    if self.角色信息['地图'] is None:
                        continue
                    self.角色信息['房间'] = self.正则['room'].search(局_登录请求返回对象.text).group(1)
                    if self.角色信息['地图'] is None:
                        continue
                    self.角色信息['角色名'] = self.正则['petName'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['myUserId'] = self.正则['myUserId'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['validateParam'] = self.正则['validateParam'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['hp_left'] = self.正则['hp_left'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['sp_left'] = self.正则['sp_left'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['hp_max'] = self.正则['hp_max'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['sp_max'] = self.正则['sp_max'].search(局_登录请求返回对象.text).group(1)
                    self.角色信息['经验百分比'] = int(re.compile(r'<\s*span\s+id\s*=\s*["\']?expBai["\']?\s*>(\d+)\s*</\s*span\s*>', re.IGNORECASE).search(局_登录请求返回对象.text).group(1))
                    if self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                        self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                        通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}角色{self.角色信息['角色名']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                        #TODO 加入命令锁,锁死命令
                    self.地址['保持在线地址']  = f"http://{self.登录配置['区服']}.pet.imop.com:8080/io/{self.角色信息['myUserId']}&{self.角色信息['validateParam']}"
                    self.控制台日志('刷新角色属性完成')
                    return True
                else:
                    self.控制台日志('未处理情况')
                    self.控制台日志(局_登录请求返回对象.text)
                    return False
        
        async def _异步在线():
            # 外层无限循环
            while True:
                while self.全局配置['在线控制器'] is False:
                    self.控制台日志('在线控制器已关闭,暂不登录')
                    await asyncio.sleep(3)
                if _确认服务器是否维护完成() and _登录():
                    if _进入角色():
                        try:
                            async with self.客户端.stream("GET", url=self.地址['保持在线地址'], cookies=self.网络信息['cookies']) as response:
                                async for line in response.aiter_text():
                                    _function_list = parse_js_code(line)# 解析js代码
                                    for _func in _function_list:
                                        # print(_func)
                                        if _func['function_name'] in [
                                                # 不处理函数列表
                                                'initWorker',   # 初始化函数
                                                # 'cls',          # 清空房间
                                                # 'cps',          # 清空房间
                                                'offOpenWin',   # 关闭窗口
                                                'clsMes',       # Npc对话框清空
                                                '_roomDesc',     # 房间描述
                                                'reOnlineNum',  # 在线状态
                                                'addUser',      # 当前房间增加角色
                                                'delUser',      # 当前房间删除角色
                                                'showRen',      # Npc图片
                                                'closeRen',     # Npc图片
                                                'showAlert',    # 白底小提示框
                                                'win',          # 战斗胜利,但是太快反应会卡指令,所以不作为结束战斗依据
                                                'closeBossZhanDouInfo',     # 疑似战斗结束,但是不能用,同上
                                                # '_showMiracle', # 无关痛痒,不知道是啥
                                                'showRenPic',   # NPC图片
                                                'showRenBigPic',   # NPC图片
                                                '_look',        # 被人观察,不处理
                                                'lost',         # 伪脱战,不处理
                                                'showLiLian',   # 历练,没必要处理
                                            ]:
                                            continue
                                        elif _func['function_name'] == '_combat':
                                            self.角色信息['战斗状态'] = True
                                            self.时间['进战'] = time.time()
                                            self.控制台日志('进入战斗')
                                        elif _func['function_name'] in ['_showMiracle', 'cls', 'cps']:
                                            self.角色信息['战斗状态'] = False
                                            self.状态['自己'] = None    # 脱战清空状态
                                            self.状态['对手'] = None    # 脱战清空状态
                                        elif _func['function_name'] == 'addCM':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['右上'].append(局_信息)
                                            self.控制台日志(f'CM右上框:{局_信息}')
                                            if len(self.消息['右上']) > 100:
                                                self.消息['右上'].pop(0)
                                        elif _func['function_name'] == 'addRM':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['左下'].append(局_信息)
                                            self.控制台日志(f'RM左下框:{局_信息}')
                                            if len(self.消息['左下']) > 100:
                                                self.消息['左下'].pop(0)
                                        elif _func['function_name'] == 'addMY':
                                            局_信息 = 通用_获取字符串时间() + '=>' + _func['parameters'][0]['value']
                                            self.消息['右下'].append(局_信息)
                                            self.控制台日志(f'MY右下框:{局_信息}')
                                            if len(self.消息['右下']) > 100:
                                                self.消息['右下'].pop(0)
                                        elif _func['function_name'] == 'addMessage':
                                            if _func['parameters'][0]['value'] == 'roomReader':
                                                局_信息 = 通用_获取字符串时间() + '=>' + _func["parameters"][1]["value"]
                                                if '你向' in 局_信息 and '发起攻击!' in 局_信息:
                                                    self.角色信息['战斗状态'] = True
                                                    self.时间['进战'] = time.time()
                                                    self.控制台日志('进入战斗')
                                                self.控制台日志(f'Message左下框:{局_信息}')
                                                self.消息['左下'].append(局_信息)
                                                if len(self.消息['左下']) > 100:
                                                    self.消息['左下'].pop(0)
                                            else:
                                                self.控制台日志('待处理addMessage')
                                                通用_发送邮件('<EMAIL>', _func)
                                        elif _func['function_name'] == '_showFightStatus':
                                            if _func['parameters'][0]['value'] == 'fighter_1_status':
                                                self.状态['自己'] = _func['parameters'][1]['value']
                                                self.控制台日志(f"战斗-自身状态:{self.状态['自己']}")
                                            if _func['parameters'][0]['value'] == 'fighter_2_status':
                                                self.状态['对手'] = _func['parameters'][1]['value']
                                                self.控制台日志(f"战斗-自身状态:{self.状态['对手']}")
                                        elif _func['function_name'] == 'state':
                                            self.控制台日志('战斗相关,暂不处理')
                                            self.角色信息['战斗状态'] = True
                                        elif _func['function_name']  in ['att1', 'att2']:
                                            self.角色信息['战斗状态'] = True
                                            self.控制台日志('战斗相关,暂不处理')
                                        elif _func['function_name'] == 'addNpcs':
                                            _s = _func['parameters'][0]['value']
                                            self.NPC['原始'] = [i.replace("'", '').replace(" ", '').split(',') for i in self.正则['npc'].findall(_s)]
                                            self.NPC['可攻击']= [i for i in self.NPC['原始'] if (i[-2] != 'true') and ('尸体' not in i[3]) and ('战斗中' not in i[3])]
                                        elif _func['function_name'] == 'setRoom':
                                            self.角色信息['房间'] = _func['parameters'][0]['value']
                                            self.控制台日志('房间', self.角色信息['房间'])
                                        elif _func['function_name'] == 'changeMap':
                                            self.角色信息['地图'] = _func['parameters'][0]['value']
                                            self.控制台日志('地图', self.角色信息['地图'])
                                        elif _func['function_name'] == 'setMaxHP':
                                            self.角色信息['hp_max'] = _func['parameters'][0]['value']
                                        elif _func['function_name'] == 'setMaxSP':
                                            self.角色信息['sp_max'] = _func['parameters'][0]['value']
                                        elif _func['function_name'] == 'setLine':
                                            局_数据 = _func['parameters'][-1]['value']
                                            if 局_数据 == '-':
                                                continue
                                            if _func['parameters'][0]['value'] == 'hpLine_left':
                                                self.角色信息['hp_left'] = int(局_数据)
                                            elif _func['parameters'][0]['value'] == 'mpLine_left':
                                                self.角色信息['sp_left'] = int(局_数据)
                                            elif _func['parameters'][0]['value'] == 'hpLine_right':
                                                pass
                                            elif _func['parameters'][0]['value'] == 'mpLine_right':
                                                pass
                                        elif _func['function_name'] == 'beiDing':
                                            self.控制台日志(f"被顶号:ip{_func['parameters'][0]['value']},延迟{self.全局配置['重连时间']}秒后重登")
                                            time.sleep(self.全局配置['重连时间'])
                                        elif _func['function_name'] == 'setFightTaskImg':
                                            # 无效,这是左上角的图标.不作为释放技能参考,且无法准确判断技能,如十字斩和强打是一个图标
                                            continue
                                        elif _func['function_name'] == 'showLeftTalk':
                                            #TODO 从这开始往下
                                            局_技能名称 = _func['parameters'][0]['value'][:-1]
                                            try:
                                                self.技能配置[局_技能名称]['剩余CD'] = self.技能配置[局_技能名称]['CD'] + 1
                                            except:
                                                pass
                                        elif _func['function_name'] == 'showRightTalk':
                                            self.控制台日志(_func)
                                        elif _func['function_name'] in ['showI', 'showIHide']:
                                            self.背包['背包道具'] = ret_packages(_func)
                                            self.背包['是否使用'] = False
                                        elif _func['function_name'] == '_skillsubs':
                                            self.技能['列表'] = eval(_func['parameters'][0]['value'].replace('false', 'False').replace('true','True'))
                                            self.技能['是否使用'] = False
                                        elif _func['function_name'] == 'setLv':
                                            self.角色信息['等级'] = int(_func['parameters'][0]['value'])
                                        elif _func['function_name'] == 'setExp':
                                            self.角色信息['经验百分比'] = int(round(int(_func['parameters'][0]['value'])/int(_func['parameters'][2]['value']),4) * 100)
                                            if self.全局配置['百分比经验上限'] < 100 and self.角色信息['经验百分比'] >= self.全局配置['百分比经验上限']:
                                                self.控制台日志(f"经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                                                if self.全局配置['邮箱']:
                                                    通用_发送邮件(self.全局配置['邮箱'], f"账号{self.登录配置['账号']}经验达到上限{self.全局配置['百分比经验上限']}%, 卡住经验")
                                                    self.命令线程锁.acquire()
                                                return
                                        elif _func['function_name'] == '_petinfo':
                                            continue
                                        elif _func['function_name'] == 'showAllotWin':
                                            print(_func)
                                            continue
                                        elif _func['function_name'] == 'addNPCC':
                                            self.消息['NPC对话'].append(_func['parameters'][0]['value'])
                                            self.控制台日志('Npc对话框:', self.消息['NPC对话'][-1])
                                        elif _func['function_name'] == 'showTask':
                                            _t = _func['parameters'][0]['value']
                                            self.任务['列表'] = js_to_python(_t)
                                            self.任务['是否使用'] = False
                                        elif _func['function_name'] == 'showItemDesc':
                                            self.道具说明['内容'] = {
                                                '名称': _func['parameters'][0]['value'],
                                                '描述': _func['parameters'][2]['value']
                                            }
                                            self.控制台日志(self.道具说明['内容'])
                                            self.道具说明['是否使用'] = False
                                        elif _func['function_name'] == 'yDeal':
                                            # 这里是交易
                                            continue
                                        else:
                                            self.控制台日志('未处理格式')
                                            self.控制台日志(_func)
                        except Exception as e:
                            with open('error.txt', 'a', encoding='utf-8') as f:
                                f.write(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '=>' + str(e) + '\n')
                await asyncio.sleep(self.全局配置['重连时间'])
                
        def _thread_worker():
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(_异步在线())

        self.在线线程 = Thread(target=_thread_worker)
        self.在线线程.start()
        
if __name__ == '__main__':
    configs = {
        "本文件名": "配置文件.json",
        "区服": "x12",
        "账号": "wk93qlyy1",
        "密码": "00000000",
        "角色序号": "0",
        "延时": 0.1,
        "非操作延时": 0.1,
        "邮箱": "<EMAIL>",
        "百分比经验上限": 99,
        "日志输出": True,
        "在线控制器": True,
        "重连时间": 300,
        "拖把每日": True,
        "在线礼包": True,
        "竞技场": True,
        "魔化": True,
        "如意": True,   
        "乐园": True,
        "沼泽": True,
        "诅咒": True,
        "无双": True,
        "逆无双": True,
        "通天": True,
        "罗汉": True
    }
    obj = MYJBase(configs)
    obj.保持在线()
    input()